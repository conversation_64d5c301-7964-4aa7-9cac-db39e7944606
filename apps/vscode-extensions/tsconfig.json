{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "dist", "lib": ["ES2020", "DOM", "DOM.Iterable"], "sourceMap": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedParameters": false, "noImplicitAny": true, "noImplicitThis": true, "noUnusedLocals": false, "allowUnreachableCode": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@workspace-gpt/confluence-utils": ["../../packages/confluence-utils/src"]}}, "exclude": ["node_modules", ".vscode-test", "webview"]}