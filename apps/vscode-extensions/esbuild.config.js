const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

const isWatch = process.argv.includes('--watch');

// Function to get all files from a directory recursively
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      fileList = getAllFiles(filePath, fileList);
    } else if (file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

// Get all worker files
const workerFiles = getAllFiles(path.join(__dirname, 'src/workers'));

// Configuration for main extension (CommonJS)
/** @type {import('esbuild').BuildOptions} */
const extensionConfig = {
  entryPoints: ['src/extension.ts'],
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist',
  format: 'cjs',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: ['vscode'],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
};

// Configuration for workers (ES modules to support @xenova/transformers)
/** @type {import('esbuild').BuildOptions} */
const workersConfig = {
  entryPoints: workerFiles,
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist',
  format: 'esm',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: [
    'vscode',
    // External native modules that should not be bundled
    'onnxruntime-node',
    'sharp',
    // External @xenova/transformers to avoid bundling issues
    '@xenova/transformers'
  ],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  banner: {
    js: `
import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
`
  },
};

async function buildAll() {
  try {
    if (isWatch) {
      // Watch mode - build both configs
      const extensionContext = await esbuild.context(extensionConfig);
      const workersContext = await esbuild.context(workersConfig);

      await Promise.all([
        extensionContext.watch(),
        workersContext.watch()
      ]);

      console.log('Watching for changes...');
    } else {
      // Build mode - build both configs
      await Promise.all([
        esbuild.build(extensionConfig),
        esbuild.build(workersConfig)
      ]);

      console.log('Build completed successfully');
    }
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

buildAll();